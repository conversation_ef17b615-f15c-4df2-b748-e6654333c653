package com.wexl.retail.qpgen.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import java.util.List;
import lombok.Builder;

public record QPGenProV2Dto() {

  @Builder
  public record QuestionSummaryRequest(
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_slug") List<String> chapterSlug,
      @JsonProperty("chapter_name") List<String> chapterName,
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("blue_printId") Long bluePrintId) {}

  @Builder
  public record QuestionSummaryResponse(
      @JsonProperty("section_name") String sectionName, List<SectionsResponse> sectionsResponses) {}

  @Builder
  public record SectionsResponse(
      @JsonProperty("section_name") String sectionName,
      @JsonProperty("section_id") Long sectionId,
      @JsonProperty("chapter_name") String chapterName,
      @JsonProperty("chapter_slug") String chapterSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("question_type") String questionType,
      @JsonProperty("question_tag_slug") String questionTagSlug,
      @JsonProperty("question_complexity_name") String questionComplexityName,
      @JsonProperty("question_complexity_slug") String questionComplexitySlug,
      @JsonProperty("total_available_questions") Long totalAvailableQuestions,
      @JsonProperty("selected_chapter_questions") Long selectedChapterQuestions,
      @JsonProperty("total_1m_questions") Long total1mQuestions,
      @JsonProperty("selected_1m_questions") Long selected1mQuestions,
      @JsonProperty("total_2m_questions") Long total2mQuestions,
      @JsonProperty("selected_2m_questions") Long selected2mQuestions,
      @JsonProperty("total_3m_questions") Long total3mQuestions,
      @JsonProperty("selected_3m_questions") Long selected3mQuestions,
      @JsonProperty("total_4m_questions") Long total4mQuestions,
      @JsonProperty("selected_4m_questions") Long selected4mQuestions,
      @JsonProperty("total_5m_questions") Long total5mQuestions,
      @JsonProperty("selected_5m_questions") Long selected5mQuestions,
      @JsonProperty("question_category_name") String questionCategoryName,
      @JsonProperty("question_category_slug") String questionCategorySlug) {}

  @Builder
  public record Request(
      @JsonProperty("blue_printId") Long bluePrintId,
      String title,
      Long marks,
      Long duration,
      @JsonProperty("board_slug") String boardSlug,
      @JsonProperty("board_name") String boardName,
      @JsonProperty("grade_name") String gradeName,
      @JsonProperty("grade_slug") String gradeSlug,
      @JsonProperty("questions_summary") List<QuestionSummaryResponse> questionSummary,
      @JsonProperty("subject_slug") String subjectSlug,
      @JsonProperty("subject_name") String subjectName,
      @JsonProperty("chapter_slug") List<String> chapterSlug,
      @JsonProperty("chapter_name") List<String> chapterName,
      @JsonProperty("list_of_subject_name") List<String> listOfSubjectName,
      @JsonProperty("list_of_subject_slug") List<String> listOfSubjectSlug,
      @JsonProperty("subject_metadata") List<SubjectMetadata> subjectMetadata) {}
  @Builder
  public record SubjectMetadata(
      @JsonProperty("subject_name") List<String> subjectName,
      @JsonProperty("subject_slug") List<String> subjectSlug) {}

  public record QuestionsResponse(
      Long marks,
      QuestionType type,
      String uuid,
      String answer,
      String chapterName,
      String chapterSlug,
      String category,
      String complexitySlug,
      String categorySlug,
      String complexity,
      String questionTags) {}

  @Builder
  public record ContentRequest(QuestionSummaryResponse questionSummary) {}

  @Builder
  public record ChapterResponse(
      @JsonProperty("complexity") String complexity,
      @JsonProperty("complexity_slug") String complexitySlug,
      @JsonProperty("category") String category,
      @JsonProperty("category_slug") String categorySlug,
      @JsonProperty("question_type") String questionType,
      @JsonProperty("selected_questions") String selectedQuestions,
      String marks) {}

  @Builder
  public record QuestionCategory(Integer id, String name, String slug, Boolean status) {}

  public record QuestionComplexities(Long id, String name, int sequence, String slug) {}
}
